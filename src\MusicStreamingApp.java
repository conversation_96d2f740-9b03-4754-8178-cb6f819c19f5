import service.MusicService;
import model.*;
import java.util.*;


/**
 * Main application class for the Music Streaming Application
 * Provides a console-based interface to interact with the music database
 */
public class MusicStreamingApp {

    private MusicService musicService;
    private Scanner scanner;

    public MusicStreamingApp() {
        this.musicService = new MusicService();
        this.scanner = new Scanner(System.in);
    }

    public static void main(String[] args) {
        MusicStreamingApp app = new MusicStreamingApp();
        app.run();
    }

    public void run() {
        System.out.println("=== Welcome to Music Streaming Application ===");

        while (true) {
            displayMainMenu();
            int choice = getIntInput("Enter your choice: ");

            switch (choice) {
                case 1:
                    manageArtists();
                    break;
                case 2:
                    manageSongs();
                    break;
                case 3:
                    manageAlbums();
                    break;
                case 4:
                    manageGenres();
                    break;
                case 5:
                    manageAwards();
                    break;
                case 6:
                    manageRelationships();
                    break;
                case 7:
                    searchAndBrowse();
                    break;
                case 0:
                    System.out.println("Thank you for using Music Streaming Application!");
                    return;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }

    private void displayMainMenu() {
        System.out.println("\n=== MAIN MENU ===");
        System.out.println("1. Manage Artists");
        System.out.println("2. Manage Songs");
        System.out.println("3. Manage Albums");
        System.out.println("4. Manage Genres");
        System.out.println("5. Manage Awards");
        System.out.println("6. Manage Relationships");
        System.out.println("7. Search and Browse");
        System.out.println("0. Exit");
    }

    private void manageArtists() {
        System.out.println("\n=== ARTIST MANAGEMENT ===");
        System.out.println("1. Add Artist");
        System.out.println("2. View All Artists");
        System.out.println("3. Search Artists");
        System.out.println("4. Update Artist");
        System.out.println("5. Delete Artist");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addArtist();
                break;
            case 2:
                viewAllArtists();
                break;
            case 3:
                searchArtists();
                break;
            case 4:
                updateArtist();
                break;
            case 5:
                deleteArtist();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addArtist() {
        System.out.println("\n--- Add New Artist ---");
        String name = getStringInput("Enter artist name: ");
        String country = getStringInput("Enter country: ");
        Integer birthYear = getOptionalIntInput("Enter birth year (or press Enter to skip): ");

        Artist artist = new Artist(name, country, birthYear);

        if (musicService.getArtistDAO().createArtist(artist)) {
            System.out.println("Artist added successfully! ID: " + artist.getArtistId());
        } else {
            System.out.println("Failed to add artist.");
        }
    }

    private void viewAllArtists() {
        System.out.println("\n--- All Artists ---");
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();

        if (artists.isEmpty()) {
            System.out.println("No artists found.");
        } else {
            for (Artist artist : artists) {
                System.out.println(artist);
            }
        }
    }

    private void searchArtists() {
        String searchTerm = getStringInput("Enter artist name to search: ");
        List<Artist> artists = musicService.getArtistDAO().searchArtistsByName(searchTerm);

        if (artists.isEmpty()) {
            System.out.println("No artists found matching: " + searchTerm);
        } else {
            System.out.println("Search results:");
            for (Artist artist : artists) {
                System.out.println(artist);
            }
        }
    }

    private void updateArtist() {
        int artistId = getIntInput("Enter artist ID to update: ");
        Artist artist = musicService.getArtistDAO().getArtistById(artistId);

        if (artist == null) {
            System.out.println("Artist not found.");
            return;
        }

        System.out.println("Current artist: " + artist);
        System.out.println("Enter new values (press Enter to keep current value):");

        String name = getOptionalStringInput("Name [" + artist.getName() + "]: ");
        if (!name.isEmpty()) artist.setName(name);

        String country = getOptionalStringInput("Country [" + artist.getCountry() + "]: ");
        if (!country.isEmpty()) artist.setCountry(country);

        String birthYearStr = getOptionalStringInput("Birth Year [" + artist.getBirthYear() + "]: ");
        if (!birthYearStr.isEmpty()) {
            try {
                artist.setBirthYear(Integer.parseInt(birthYearStr));
            } catch (NumberFormatException e) {
                System.out.println("Invalid birth year format.");
                return;
            }
        }

        if (musicService.getArtistDAO().updateArtist(artist)) {
            System.out.println("Artist updated successfully!");
        } else {
            System.out.println("Failed to update artist.");
        }
    }

    private void deleteArtist() {
        int artistId = getIntInput("Enter artist ID to delete: ");
        Artist artist = musicService.getArtistDAO().getArtistById(artistId);

        if (artist == null) {
            System.out.println("Artist not found.");
            return;
        }

        System.out.println("Artist to delete: " + artist);
        String confirm = getStringInput("Are you sure? (yes/no): ");

        if (confirm.equalsIgnoreCase("yes")) {
            if (musicService.getArtistDAO().deleteArtist(artistId)) {
                System.out.println("Artist deleted successfully!");
            } else {
                System.out.println("Failed to delete artist.");
            }
        } else {
            System.out.println("Deletion cancelled.");
        }
    }

    private void manageSongs() {
        System.out.println("\n=== SONG MANAGEMENT ===");
        System.out.println("1. Add Song");
        System.out.println("2. View All Songs");
        System.out.println("3. Search Songs");
        System.out.println("4. View Songs by Album");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addSong();
                break;
            case 2:
                viewAllSongs();
                break;
            case 3:
                searchSongs();
                break;
            case 4:
                viewSongsByAlbum();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addSong() {
        System.out.println("\n--- Add New Song ---");
        String title = getStringInput("Enter song title: ");
        Integer duration = getOptionalIntInput("Enter duration in seconds (or press Enter to skip): ");
        Integer releaseYear = getOptionalIntInput("Enter release year (or press Enter to skip): ");

        Song song = new Song(title, duration, releaseYear);

        if (musicService.getSongDAO().createSong(song)) {
            System.out.println("Song added successfully! ID: " + song.getSongId());
            System.out.println("Note: To add this song to an album, use 'Manage Albums' -> 'Manage Album-Song Relationships'");
        } else {
            System.out.println("Failed to add song.");
        }
    }

    private void viewAllSongs() {
        System.out.println("\n--- All Songs ---");
        List<Song> songs = musicService.getSongDAO().getAllSongs();

        if (songs.isEmpty()) {
            System.out.println("No songs found.");
        } else {
            for (Song song : songs) {
                System.out.println(song + " [Duration: " + song.getFormattedDuration() + "]");
            }
        }
    }

    private void searchSongs() {
        String searchTerm = getStringInput("Enter song title to search: ");
        List<Song> songs = musicService.getSongDAO().searchSongsByTitle(searchTerm);

        if (songs.isEmpty()) {
            System.out.println("No songs found matching: " + searchTerm);
        } else {
            System.out.println("Search results:");
            for (Song song : songs) {
                System.out.println(song + " [Duration: " + song.getFormattedDuration() + "]");
            }
        }
    }

    private void viewSongsByAlbum() {
        int albumId = getIntInput("Enter album ID: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByAlbum(albumId);

        if (songs.isEmpty()) {
            System.out.println("No songs found for album: " + album.getTitle());
        } else {
            System.out.println("Songs in '" + album.getTitle() + "':");
            for (Song song : songs) {
                System.out.println(song + " [Duration: " + song.getFormattedDuration() + "]");
            }

            int totalSongs = musicService.getTotalSongsInAlbum(albumId);
            System.out.println("\nTotal songs in album: " + totalSongs);
        }
    }

    // Album Management
    private void manageAlbums() {
        System.out.println("\n=== ALBUM MANAGEMENT ===");
        System.out.println("1. Add Album");
        System.out.println("2. View All Albums");
        System.out.println("3. Search Albums");
        System.out.println("4. Update Album");
        System.out.println("5. Delete Album");
        System.out.println("6. Manage Album-Song Relationships");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addAlbum();
                break;
            case 2:
                viewAllAlbums();
                break;
            case 3:
                searchAlbums();
                break;
            case 4:
                updateAlbum();
                break;
            case 5:
                deleteAlbum();
                break;
            case 6:
                manageAlbumSongRelationships();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addAlbum() {
        System.out.println("\n--- Add New Album ---");
        String title = getStringInput("Enter album title: ");
        Integer releaseYear = getOptionalIntInput("Enter release year (or press Enter to skip): ");

        Album album = new Album(title, releaseYear);

        if (musicService.getAlbumDAO().createAlbum(album)) {
            System.out.println("Album added successfully! ID: " + album.getAlbumId());
        } else {
            System.out.println("Failed to add album.");
        }
    }

    private void viewAllAlbums() {
        System.out.println("\n--- All Albums ---");
        List<Album> albums = musicService.getAlbumDAO().getAllAlbums();

        if (albums.isEmpty()) {
            System.out.println("No albums found.");
        } else {
            for (Album album : albums) {
                System.out.println(album);
            }
        }
    }

    private void searchAlbums() {
        String searchTerm = getStringInput("Enter album title to search: ");
        List<Album> albums = musicService.getAlbumDAO().searchAlbumsByTitle(searchTerm);

        if (albums.isEmpty()) {
            System.out.println("No albums found matching: " + searchTerm);
        } else {
            System.out.println("Search results:");
            for (Album album : albums) {
                System.out.println(album);
            }
        }
    }

    private void updateAlbum() {
        int albumId = getIntInput("Enter album ID to update: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        System.out.println("Current album: " + album);
        System.out.println("Enter new values (press Enter to keep current value):");

        String title = getOptionalStringInput("Title [" + album.getTitle() + "]: ");
        if (!title.isEmpty()) album.setTitle(title);

        String releaseYearStr = getOptionalStringInput("Release Year [" + album.getReleaseYear() + "]: ");
        if (!releaseYearStr.isEmpty()) {
            try {
                album.setReleaseYear(Integer.parseInt(releaseYearStr));
            } catch (NumberFormatException e) {
                System.out.println("Invalid release year format.");
                return;
            }
        }



        if (musicService.getAlbumDAO().updateAlbum(album)) {
            System.out.println("Album updated successfully!");
        } else {
            System.out.println("Failed to update album.");
        }
    }

    private void deleteAlbum() {
        int albumId = getIntInput("Enter album ID to delete: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        System.out.println("Album to delete: " + album);
        String confirm = getStringInput("Are you sure? (yes/no): ");

        if (confirm.equalsIgnoreCase("yes")) {
            if (musicService.getAlbumDAO().deleteAlbum(albumId)) {
                System.out.println("Album deleted successfully!");
            } else {
                System.out.println("Failed to delete album.");
            }
        } else {
            System.out.println("Deletion cancelled.");
        }
    }

    private void manageAlbumSongRelationships() {
        System.out.println("\n=== ALBUM-SONG RELATIONSHIPS ===");
        System.out.println("1. Add Song to Album");
        System.out.println("2. View Songs in Album");
        System.out.println("3. Remove Song from Album");
        System.out.println("4. View Album Song Count");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addSongToAlbumRelationship();
                break;
            case 2:
                viewSongsInAlbum();
                break;
            case 3:
                removeSongFromAlbumRelationship();
                break;
            case 4:
                viewAlbumSongCount();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addSongToAlbumRelationship() {
        System.out.println("\n--- Add Song to Album ---");

        // Show available albums
        List<Album> albums = musicService.getAlbumDAO().getAllAlbums();
        if (albums.isEmpty()) {
            System.out.println("No albums available. Please add albums first.");
            return;
        }

        System.out.println("Available Albums:");
        for (Album album : albums) {
            System.out.println(album.getAlbumId() + ". " + album.getTitle());
        }

        int albumId = getIntInput("Enter album ID: ");

        // Show available songs
        List<Song> songs = musicService.getSongDAO().getAllSongs();
        if (songs.isEmpty()) {
            System.out.println("No songs available. Please add songs first.");
            return;
        }

        System.out.println("Available Songs:");
        for (Song song : songs) {
            System.out.println(song.getSongId() + ". " + song.getTitle());
        }

        int songId = getIntInput("Enter song ID: ");
        int noOfSongs = getIntInput("Enter total number of songs in this album: ");

        if (musicService.addSongToAlbum(albumId, songId, noOfSongs)) {
            System.out.println("Song added to album successfully!");
        } else {
            System.out.println("Failed to add song to album.");
        }
    }

    private void viewSongsInAlbum() {
        int albumId = getIntInput("Enter album ID: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByAlbum(albumId);
        System.out.println("\nSongs in '" + album.getTitle() + "':");

        if (songs.isEmpty()) {
            System.out.println("No songs found in this album.");
        } else {
            for (Song song : songs) {
                System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
            }

            int totalSongs = musicService.getTotalSongsInAlbum(albumId);
            System.out.println("\nTotal songs in album: " + totalSongs);
        }
    }

    private void removeSongFromAlbumRelationship() {
        int albumId = getIntInput("Enter album ID: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByAlbum(albumId);
        if (songs.isEmpty()) {
            System.out.println("No songs found in this album.");
            return;
        }

        System.out.println("Songs in '" + album.getTitle() + "':");
        for (Song song : songs) {
            System.out.println(song.getSongId() + ". " + song.getTitle());
        }

        int songId = getIntInput("Enter song ID to remove: ");

        if (musicService.removeSongFromAlbum(albumId, songId)) {
            System.out.println("Song removed from album successfully!");
        } else {
            System.out.println("Failed to remove song from album.");
        }
    }

    private void viewAlbumSongCount() {
        int albumId = getIntInput("Enter album ID: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        int songCount = musicService.getAlbumDAO().getSongCountForAlbum(albumId);
        int totalSongs = musicService.getTotalSongsInAlbum(albumId);

        System.out.println("\nAlbum: " + album.getTitle());
        System.out.println("Songs currently in database for this album: " + songCount);
        System.out.println("Total songs in album (from metadata): " + totalSongs);
    }

    // Genre Management
    private void manageGenres() {
        System.out.println("\n=== GENRE MANAGEMENT ===");
        System.out.println("1. Add Genre");
        System.out.println("2. View All Genres");
        System.out.println("3. Search Genres");
        System.out.println("4. Update Genre");
        System.out.println("5. Delete Genre");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addGenre();
                break;
            case 2:
                viewAllGenres();
                break;
            case 3:
                searchGenres();
                break;
            case 4:
                updateGenre();
                break;
            case 5:
                deleteGenre();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addGenre() {
        System.out.println("\n--- Add New Genre ---");
        String name = getStringInput("Enter genre name: ");
        String description = getStringInput("Enter genre description: ");

        Genre genre = new Genre(name, description);

        if (musicService.getGenreDAO().createGenre(genre)) {
            System.out.println("Genre added successfully! ID: " + genre.getGenreId());
        } else {
            System.out.println("Failed to add genre.");
        }
    }

    private void viewAllGenres() {
        System.out.println("\n--- All Genres ---");
        List<Genre> genres = musicService.getGenreDAO().getAllGenres();

        if (genres.isEmpty()) {
            System.out.println("No genres found.");
        } else {
            for (Genre genre : genres) {
                System.out.println(genre);
            }
        }
    }

    private void searchGenres() {
        String searchTerm = getStringInput("Enter genre name to search: ");
        List<Genre> genres = musicService.getGenreDAO().searchGenresByName(searchTerm);

        if (genres.isEmpty()) {
            System.out.println("No genres found matching: " + searchTerm);
        } else {
            System.out.println("Search results:");
            for (Genre genre : genres) {
                System.out.println(genre);
            }
        }
    }

    private void updateGenre() {
        int genreId = getIntInput("Enter genre ID to update: ");
        Genre genre = musicService.getGenreDAO().getGenreById(genreId);

        if (genre == null) {
            System.out.println("Genre not found.");
            return;
        }

        System.out.println("Current genre: " + genre);
        System.out.println("Enter new values (press Enter to keep current value):");

        String name = getOptionalStringInput("Name [" + genre.getName() + "]: ");
        if (!name.isEmpty()) genre.setName(name);

        String description = getOptionalStringInput("Description [" + genre.getDescription() + "]: ");
        if (!description.isEmpty()) genre.setDescription(description);

        if (musicService.getGenreDAO().updateGenre(genre)) {
            System.out.println("Genre updated successfully!");
        } else {
            System.out.println("Failed to update genre.");
        }
    }

    private void deleteGenre() {
        int genreId = getIntInput("Enter genre ID to delete: ");
        Genre genre = musicService.getGenreDAO().getGenreById(genreId);

        if (genre == null) {
            System.out.println("Genre not found.");
            return;
        }

        System.out.println("Genre to delete: " + genre);
        String confirm = getStringInput("Are you sure? (yes/no): ");

        if (confirm.equalsIgnoreCase("yes")) {
            if (musicService.getGenreDAO().deleteGenre(genreId)) {
                System.out.println("Genre deleted successfully!");
            } else {
                System.out.println("Failed to delete genre.");
            }
        } else {
            System.out.println("Deletion cancelled.");
        }
    }

    // Award Management
    private void manageAwards() {
        System.out.println("\n=== AWARD MANAGEMENT ===");
        System.out.println("1. Add Award");
        System.out.println("2. View All Awards");
        System.out.println("3. Search Awards");
        System.out.println("4. View Awards by Year");
        System.out.println("5. Update Award");
        System.out.println("6. Delete Award");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addAward();
                break;
            case 2:
                viewAllAwards();
                break;
            case 3:
                searchAwards();
                break;
            case 4:
                viewAwardsByYear();
                break;
            case 5:
                updateAward();
                break;
            case 6:
                deleteAward();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addAward() {
        System.out.println("\n--- Add New Award ---");
        String awardName = getStringInput("Enter award name: ");
        int yearWon = getIntInput("Enter year won: ");

        Award award = new Award(awardName, yearWon);

        if (musicService.getAwardDAO().createAward(award)) {
            System.out.println("Award added successfully! ID: " + award.getAwardId());
        } else {
            System.out.println("Failed to add award.");
        }
    }

    private void viewAllAwards() {
        System.out.println("\n--- All Awards ---");
        List<Award> awards = musicService.getAwardDAO().getAllAwards();

        if (awards.isEmpty()) {
            System.out.println("No awards found.");
        } else {
            for (Award award : awards) {
                System.out.println(award);
            }
        }
    }

    private void searchAwards() {
        String searchTerm = getStringInput("Enter award name to search: ");
        List<Award> awards = musicService.getAwardDAO().searchAwardsByName(searchTerm);

        if (awards.isEmpty()) {
            System.out.println("No awards found matching: " + searchTerm);
        } else {
            System.out.println("Search results:");
            for (Award award : awards) {
                System.out.println(award);
            }
        }
    }

    private void viewAwardsByYear() {
        int year = getIntInput("Enter year: ");
        List<Award> awards = musicService.getAwardDAO().getAwardsByYear(year);

        if (awards.isEmpty()) {
            System.out.println("No awards found for year: " + year);
        } else {
            System.out.println("Awards in " + year + ":");
            for (Award award : awards) {
                System.out.println(award);
            }
        }
    }

    private void updateAward() {
        int awardId = getIntInput("Enter award ID to update: ");
        Award award = musicService.getAwardDAO().getAwardById(awardId);

        if (award == null) {
            System.out.println("Award not found.");
            return;
        }

        System.out.println("Current award: " + award);
        System.out.println("Enter new values (press Enter to keep current value):");

        String awardName = getOptionalStringInput("Award Name [" + award.getAwardName() + "]: ");
        if (!awardName.isEmpty()) award.setAwardName(awardName);

        String yearWonStr = getOptionalStringInput("Year Won [" + award.getYearWon() + "]: ");
        if (!yearWonStr.isEmpty()) {
            try {
                award.setYearWon(Integer.parseInt(yearWonStr));
            } catch (NumberFormatException e) {
                System.out.println("Invalid year format.");
                return;
            }
        }

        if (musicService.getAwardDAO().updateAward(award)) {
            System.out.println("Award updated successfully!");
        } else {
            System.out.println("Failed to update award.");
        }
    }

    private void deleteAward() {
        int awardId = getIntInput("Enter award ID to delete: ");
        Award award = musicService.getAwardDAO().getAwardById(awardId);

        if (award == null) {
            System.out.println("Award not found.");
            return;
        }

        System.out.println("Award to delete: " + award);
        String confirm = getStringInput("Are you sure? (yes/no): ");

        if (confirm.equalsIgnoreCase("yes")) {
            if (musicService.getAwardDAO().deleteAward(awardId)) {
                System.out.println("Award deleted successfully!");
            } else {
                System.out.println("Failed to delete award.");
            }
        } else {
            System.out.println("Deletion cancelled.");
        }
    }

    // Relationship Management
    private void manageRelationships() {
        System.out.println("\n=== RELATIONSHIP MANAGEMENT ===");
        System.out.println("1. Album-Song Relationships (CONTAINS)");
        System.out.println("2. Artist-Song Relationships (PERFORMS)");
        System.out.println("3. Artist-Award Relationships (RECEIVES)");
        System.out.println("4. Song-Genre Relationships (BELONGS_TO)");
        System.out.println("5. View Relationships");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                manageAlbumSongRelationships();
                break;
            case 2:
                manageArtistSongRelationships();
                break;
            case 3:
                manageArtistAwardRelationships();
                break;
            case 4:
                manageSongGenreRelationships();
                break;
            case 5:
                viewRelationships();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void manageArtistSongRelationships() {
        System.out.println("\n=== ARTIST-SONG RELATIONSHIPS ===");
        System.out.println("1. Add Performance");
        System.out.println("2. View Songs by Artist");
        System.out.println("3. View Artists by Song");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addPerformance();
                break;
            case 2:
                viewSongsByArtistId();
                break;
            case 3:
                viewArtistsBySongId();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addPerformance() {
        System.out.println("\n--- Add Performance ---");

        // Show available artists
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();
        if (artists.isEmpty()) {
            System.out.println("No artists available. Please add artists first.");
            return;
        }

        System.out.println("Available Artists:");
        for (Artist artist : artists) {
            System.out.println(artist.getArtistId() + ". " + artist.getName());
        }

        int artistId = getIntInput("Enter artist ID: ");

        // Show available songs
        List<Song> songs = musicService.getSongDAO().getAllSongs();
        if (songs.isEmpty()) {
            System.out.println("No songs available. Please add songs first.");
            return;
        }

        System.out.println("Available Songs:");
        for (Song song : songs) {
            System.out.println(song.getSongId() + ". " + song.getTitle());
        }

        int songId = getIntInput("Enter song ID: ");
        String venue = getStringInput("Enter venue: ");

        if (musicService.addPerformance(artistId, songId, venue)) {
            System.out.println("Performance added successfully!");
        } else {
            System.out.println("Failed to add performance.");
        }
    }

    private void viewSongsByArtistId() {
        int artistId = getIntInput("Enter artist ID: ");
        Artist artist = musicService.getArtistDAO().getArtistById(artistId);

        if (artist == null) {
            System.out.println("Artist not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByArtist(artistId);
        System.out.println("\nSongs performed by " + artist.getName() + ":");

        if (songs.isEmpty()) {
            System.out.println("No songs found for this artist.");
        } else {
            for (Song song : songs) {
                System.out.println("- " + song.getTitle() + " [Duration: " + song.getFormattedDuration() + "]");
            }
        }
    }

    private void viewArtistsBySongId() {
        int songId = getIntInput("Enter song ID: ");
        Song song = musicService.getSongDAO().getSongById(songId);

        if (song == null) {
            System.out.println("Song not found.");
            return;
        }

        List<Artist> artists = musicService.getArtistsBySong(songId);
        System.out.println("\nArtists who perform '" + song.getTitle() + "':");

        if (artists.isEmpty()) {
            System.out.println("No artists found for this song.");
        } else {
            for (Artist artist : artists) {
                System.out.println("- " + artist.getName() + " (" + artist.getCountry() + ")");
            }
        }
    }

    private void manageArtistAwardRelationships() {
        System.out.println("\n=== ARTIST-AWARD RELATIONSHIPS ===");
        System.out.println("1. Add Award to Artist");
        System.out.println("2. View Awards by Artist");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addAwardToArtist();
                break;
            case 2:
                viewAwardsByArtistId();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addAwardToArtist() {
        System.out.println("\n--- Add Award to Artist ---");

        // Show available artists
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();
        if (artists.isEmpty()) {
            System.out.println("No artists available. Please add artists first.");
            return;
        }

        System.out.println("Available Artists:");
        for (Artist artist : artists) {
            System.out.println(artist.getArtistId() + ". " + artist.getName());
        }

        int artistId = getIntInput("Enter artist ID: ");

        // Show available awards
        List<Award> awards = musicService.getAwardDAO().getAllAwards();
        if (awards.isEmpty()) {
            System.out.println("No awards available. Please add awards first.");
            return;
        }

        System.out.println("Available Awards:");
        for (Award award : awards) {
            System.out.println(award.getAwardId() + ". " + award.getAwardName() + " (" + award.getYearWon() + ")");
        }

        int awardId = getIntInput("Enter award ID: ");
        String role = getStringInput("Enter role (e.g., 'Lead Singer', 'Producer', etc.): ");

        if (musicService.addAwardToArtist(artistId, awardId, role)) {
            System.out.println("Award added to artist successfully!");
        } else {
            System.out.println("Failed to add award to artist.");
        }
    }

    private void viewAwardsByArtistId() {
        int artistId = getIntInput("Enter artist ID: ");
        Artist artist = musicService.getArtistDAO().getArtistById(artistId);

        if (artist == null) {
            System.out.println("Artist not found.");
            return;
        }

        List<Award> awards = musicService.getAwardsByArtist(artistId);
        System.out.println("\nAwards received by " + artist.getName() + ":");

        if (awards.isEmpty()) {
            System.out.println("No awards found for this artist.");
        } else {
            for (Award award : awards) {
                System.out.println("- " + award.getAwardName() + " (" + award.getYearWon() + ")");
            }
        }
    }

    private void manageSongGenreRelationships() {
        System.out.println("\n=== SONG-GENRE RELATIONSHIPS ===");
        System.out.println("1. Add Genre to Song");
        System.out.println("2. View Genres by Song");
        System.out.println("3. View Songs by Genre");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addGenreToSong();
                break;
            case 2:
                viewGenresBySongId();
                break;
            case 3:
                viewSongsByGenreId();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void addGenreToSong() {
        System.out.println("\n--- Add Genre to Song ---");

        // Show available songs
        List<Song> songs = musicService.getSongDAO().getAllSongs();
        if (songs.isEmpty()) {
            System.out.println("No songs available. Please add songs first.");
            return;
        }

        System.out.println("Available Songs:");
        for (Song song : songs) {
            System.out.println(song.getSongId() + ". " + song.getTitle());
        }

        int songId = getIntInput("Enter song ID: ");

        // Show available genres
        List<Genre> genres = musicService.getGenreDAO().getAllGenres();
        if (genres.isEmpty()) {
            System.out.println("No genres available. Please add genres first.");
            return;
        }

        System.out.println("Available Genres:");
        for (Genre genre : genres) {
            System.out.println(genre.getGenreId() + ". " + genre.getName());
        }

        int genreId = getIntInput("Enter genre ID: ");
        String assignedBy = getStringInput("Enter assigned by (e.g., 'Music Critic', 'Algorithm', etc.): ");

        if (musicService.addGenreToSong(songId, genreId, assignedBy)) {
            System.out.println("Genre added to song successfully!");
        } else {
            System.out.println("Failed to add genre to song.");
        }
    }

    private void viewGenresBySongId() {
        int songId = getIntInput("Enter song ID: ");
        Song song = musicService.getSongDAO().getSongById(songId);

        if (song == null) {
            System.out.println("Song not found.");
            return;
        }

        List<Genre> genres = musicService.getGenresBySong(songId);
        System.out.println("\nGenres for '" + song.getTitle() + "':");

        if (genres.isEmpty()) {
            System.out.println("No genres found for this song.");
        } else {
            for (Genre genre : genres) {
                System.out.println("- " + genre.getName() + ": " + genre.getDescription());
            }
        }
    }

    private void viewSongsByGenreId() {
        int genreId = getIntInput("Enter genre ID: ");
        Genre genre = musicService.getGenreDAO().getGenreById(genreId);

        if (genre == null) {
            System.out.println("Genre not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByGenre(genreId);
        System.out.println("\nSongs in '" + genre.getName() + "' genre:");

        if (songs.isEmpty()) {
            System.out.println("No songs found for this genre.");
        } else {
            for (Song song : songs) {
                System.out.println("- " + song.getTitle() + " [Duration: " + song.getFormattedDuration() + "]");
            }
        }
    }

    private void viewRelationships() {
        System.out.println("\n=== VIEW ALL RELATIONSHIPS ===");
        System.out.println("1. All Album-Song Relationships");
        System.out.println("2. All Artist-Song Performances");
        System.out.println("3. All Artist-Award Relationships");
        System.out.println("4. All Song-Genre Relationships");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                viewAllAlbumSongs();
                break;
            case 2:
                viewAllPerformances();
                break;
            case 3:
                viewAllArtistAwards();
                break;
            case 4:
                viewAllSongGenres();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void viewAllPerformances() {
        System.out.println("\n--- All Artist-Song Performances ---");
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();

        for (Artist artist : artists) {
            List<Song> songs = musicService.getSongsByArtist(artist.getArtistId());
            if (!songs.isEmpty()) {
                System.out.println("\n" + artist.getName() + " performs:");
                for (Song song : songs) {
                    System.out.println("  - " + song.getTitle());
                }
            }
        }
    }

    private void viewAllArtistAwards() {
        System.out.println("\n--- All Artist-Award Relationships ---");
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();

        for (Artist artist : artists) {
            List<Award> awards = musicService.getAwardsByArtist(artist.getArtistId());
            if (!awards.isEmpty()) {
                System.out.println("\n" + artist.getName() + " received:");
                for (Award award : awards) {
                    System.out.println("  - " + award.getAwardName() + " (" + award.getYearWon() + ")");
                }
            }
        }
    }

    private void viewAllSongGenres() {
        System.out.println("\n--- All Song-Genre Relationships ---");
        List<Song> songs = musicService.getSongDAO().getAllSongs();

        for (Song song : songs) {
            List<Genre> genres = musicService.getGenresBySong(song.getSongId());
            if (!genres.isEmpty()) {
                System.out.println("\n'" + song.getTitle() + "' belongs to:");
                for (Genre genre : genres) {
                    System.out.println("  - " + genre.getName());
                }
            }
        }
    }

    private void viewAllAlbumSongs() {
        System.out.println("\n--- All Album-Song Relationships (CONTAINS) ---");
        List<Album> albums = musicService.getAlbumDAO().getAllAlbums();

        for (Album album : albums) {
            List<Song> songs = musicService.getSongsByAlbum(album.getAlbumId());
            if (!songs.isEmpty()) {
                int totalSongs = musicService.getTotalSongsInAlbum(album.getAlbumId());
                System.out.println("\n'" + album.getTitle() + "' (" + album.getReleaseYear() + ") contains " + songs.size() + " songs (Total: " + totalSongs + "):");
                for (Song song : songs) {
                    System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
                }
            }
        }
    }

    // Search and Browse
    private void searchAndBrowse() {
        System.out.println("\n=== SEARCH AND BROWSE ===");
        System.out.println("1. Global Search");
        System.out.println("2. Browse by Category");
        System.out.println("3. Advanced Search");
        System.out.println("4. Statistics");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                globalSearch();
                break;
            case 2:
                browseByCategory();
                break;
            case 3:
                advancedSearch();
                break;
            case 4:
                showStatistics();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void globalSearch() {
        System.out.println("\n--- Global Search ---");
        String searchTerm = getStringInput("Enter search term: ");

        System.out.println("\nSearch Results for: '" + searchTerm + "'");

        // Search artists
        List<Artist> artists = musicService.getArtistDAO().searchArtistsByName(searchTerm);
        if (!artists.isEmpty()) {
            System.out.println("\nArtists:");
            for (Artist artist : artists) {
                System.out.println("  - " + artist.getName() + " (" + artist.getCountry() + ")");
            }
        }

        // Search songs
        List<Song> songs = musicService.getSongDAO().searchSongsByTitle(searchTerm);
        if (!songs.isEmpty()) {
            System.out.println("\nSongs:");
            for (Song song : songs) {
                System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
            }
        }

        // Search albums
        List<Album> albums = musicService.getAlbumDAO().searchAlbumsByTitle(searchTerm);
        if (!albums.isEmpty()) {
            System.out.println("\nAlbums:");
            for (Album album : albums) {
                System.out.println("  - " + album.getTitle() + " (" + album.getReleaseYear() + ")");
            }
        }

        // Search genres
        List<Genre> genres = musicService.getGenreDAO().searchGenresByName(searchTerm);
        if (!genres.isEmpty()) {
            System.out.println("\nGenres:");
            for (Genre genre : genres) {
                System.out.println("  - " + genre.getName() + ": " + genre.getDescription());
            }
        }

        // Search awards
        List<Award> awards = musicService.getAwardDAO().searchAwardsByName(searchTerm);
        if (!awards.isEmpty()) {
            System.out.println("\nAwards:");
            for (Award award : awards) {
                System.out.println("  - " + award.getAwardName() + " (" + award.getYearWon() + ")");
            }
        }

        if (artists.isEmpty() && songs.isEmpty() && albums.isEmpty() && genres.isEmpty() && awards.isEmpty()) {
            System.out.println("No results found for: '" + searchTerm + "'");
        }
    }

    private void browseByCategory() {
        System.out.println("\n--- Browse by Category ---");
        System.out.println("1. Browse by Genre");
        System.out.println("2. Browse by Year");
        System.out.println("3. Browse by Country");
        System.out.println("4. Browse by Album");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                browseByGenre();
                break;
            case 2:
                browseByYear();
                break;
            case 3:
                browseByCountry();
                break;
            case 4:
                browseByAlbum();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void browseByGenre() {
        System.out.println("\n--- Browse by Genre ---");
        List<Genre> genres = musicService.getGenreDAO().getAllGenres();

        if (genres.isEmpty()) {
            System.out.println("No genres available.");
            return;
        }

        System.out.println("Available Genres:");
        for (Genre genre : genres) {
            System.out.println(genre.getGenreId() + ". " + genre.getName());
        }

        int genreId = getIntInput("Enter genre ID to browse: ");
        Genre selectedGenre = musicService.getGenreDAO().getGenreById(genreId);

        if (selectedGenre == null) {
            System.out.println("Genre not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByGenre(genreId);
        System.out.println("\nSongs in '" + selectedGenre.getName() + "' genre:");

        if (songs.isEmpty()) {
            System.out.println("No songs found in this genre.");
        } else {
            for (Song song : songs) {
                System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
            }
        }
    }

    private void browseByYear() {
        int year = getIntInput("Enter year to browse: ");

        System.out.println("\nContent from " + year + ":");

        // Songs from that year
        List<Song> allSongs = musicService.getSongDAO().getAllSongs();
        List<Song> songsFromYear = new ArrayList<>();
        for (Song song : allSongs) {
            if (song.getReleaseYear() != null && song.getReleaseYear() == year) {
                songsFromYear.add(song);
            }
        }

        if (!songsFromYear.isEmpty()) {
            System.out.println("\nSongs:");
            for (Song song : songsFromYear) {
                System.out.println("  - " + song.getTitle());
            }
        }

        // Albums from that year
        List<Album> allAlbums = musicService.getAlbumDAO().getAllAlbums();
        List<Album> albumsFromYear = new ArrayList<>();
        for (Album album : allAlbums) {
            if (album.getReleaseYear() != null && album.getReleaseYear() == year) {
                albumsFromYear.add(album);
            }
        }

        if (!albumsFromYear.isEmpty()) {
            System.out.println("\nAlbums:");
            for (Album album : albumsFromYear) {
                System.out.println("  - " + album.getTitle());
            }
        }

        // Awards from that year
        List<Award> awards = musicService.getAwardDAO().getAwardsByYear(year);
        if (!awards.isEmpty()) {
            System.out.println("\nAwards:");
            for (Award award : awards) {
                System.out.println("  - " + award.getAwardName());
            }
        }

        if (songsFromYear.isEmpty() && albumsFromYear.isEmpty() && awards.isEmpty()) {
            System.out.println("No content found for year " + year);
        }
    }

    private void browseByCountry() {
        String country = getStringInput("Enter country to browse: ");

        List<Artist> allArtists = musicService.getArtistDAO().getAllArtists();
        List<Artist> artistsFromCountry = new ArrayList<>();

        for (Artist artist : allArtists) {
            if (artist.getCountry() != null &&
                artist.getCountry().toLowerCase().contains(country.toLowerCase())) {
                artistsFromCountry.add(artist);
            }
        }

        System.out.println("\nArtists from " + country + ":");

        if (artistsFromCountry.isEmpty()) {
            System.out.println("No artists found from " + country);
        } else {
            for (Artist artist : artistsFromCountry) {
                System.out.println("  - " + artist.getName() + " (Born: " + artist.getBirthYear() + ")");

                // Show their songs
                List<Song> songs = musicService.getSongsByArtist(artist.getArtistId());
                if (!songs.isEmpty()) {
                    System.out.println("    Songs:");
                    for (Song song : songs) {
                        System.out.println("      * " + song.getTitle());
                    }
                }
            }
        }
    }

    private void browseByAlbum() {
        System.out.println("\n--- Browse by Album ---");
        List<Album> albums = musicService.getAlbumDAO().getAllAlbums();

        if (albums.isEmpty()) {
            System.out.println("No albums available.");
            return;
        }

        System.out.println("Available Albums:");
        for (Album album : albums) {
            System.out.println(album.getAlbumId() + ". " + album.getTitle() + " (" + album.getReleaseYear() + ")");
        }

        int albumId = getIntInput("Enter album ID to browse: ");
        Album selectedAlbum = musicService.getAlbumDAO().getAlbumById(albumId);

        if (selectedAlbum == null) {
            System.out.println("Album not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByAlbum(albumId);
        System.out.println("\nSongs in '" + selectedAlbum.getTitle() + "':");

        if (songs.isEmpty()) {
            System.out.println("No songs found in this album.");
        } else {
            for (Song song : songs) {
                System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
            }

            int totalSongs = musicService.getTotalSongsInAlbum(albumId);
            System.out.println("\nTotal songs in album: " + totalSongs);
        }
    }

    private void advancedSearch() {
        System.out.println("\n--- Advanced Search ---");
        System.out.println("1. Search Songs by Duration Range");
        System.out.println("2. Search Artists by Birth Year Range");
        System.out.println("3. Search Albums by Release Year Range");
        System.out.println("4. Search Multi-criteria");

        int choice = getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                searchSongsByDuration();
                break;
            case 2:
                searchArtistsByBirthYear();
                break;
            case 3:
                searchAlbumsByReleaseYear();
                break;
            case 4:
                searchMultiCriteria();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }

    private void searchSongsByDuration() {
        int minDuration = getIntInput("Enter minimum duration (seconds): ");
        int maxDuration = getIntInput("Enter maximum duration (seconds): ");

        List<Song> allSongs = musicService.getSongDAO().getAllSongs();
        List<Song> filteredSongs = new ArrayList<>();

        for (Song song : allSongs) {
            if (song.getDuration() != null &&
                song.getDuration() >= minDuration &&
                song.getDuration() <= maxDuration) {
                filteredSongs.add(song);
            }
        }

        System.out.println("\nSongs with duration between " + minDuration + " and " + maxDuration + " seconds:");

        if (filteredSongs.isEmpty()) {
            System.out.println("No songs found in this duration range.");
        } else {
            for (Song song : filteredSongs) {
                System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
            }
        }
    }

    private void searchArtistsByBirthYear() {
        int minYear = getIntInput("Enter minimum birth year: ");
        int maxYear = getIntInput("Enter maximum birth year: ");

        List<Artist> allArtists = musicService.getArtistDAO().getAllArtists();
        List<Artist> filteredArtists = new ArrayList<>();

        for (Artist artist : allArtists) {
            if (artist.getBirthYear() != null &&
                artist.getBirthYear() >= minYear &&
                artist.getBirthYear() <= maxYear) {
                filteredArtists.add(artist);
            }
        }

        System.out.println("\nArtists born between " + minYear + " and " + maxYear + ":");

        if (filteredArtists.isEmpty()) {
            System.out.println("No artists found in this birth year range.");
        } else {
            for (Artist artist : filteredArtists) {
                System.out.println("  - " + artist.getName() + " (" + artist.getBirthYear() + ", " + artist.getCountry() + ")");
            }
        }
    }

    private void searchAlbumsByReleaseYear() {
        int minYear = getIntInput("Enter minimum release year: ");
        int maxYear = getIntInput("Enter maximum release year: ");

        List<Album> allAlbums = musicService.getAlbumDAO().getAllAlbums();
        List<Album> filteredAlbums = new ArrayList<>();

        for (Album album : allAlbums) {
            if (album.getReleaseYear() != null &&
                album.getReleaseYear() >= minYear &&
                album.getReleaseYear() <= maxYear) {
                filteredAlbums.add(album);
            }
        }

        System.out.println("\nAlbums released between " + minYear + " and " + maxYear + ":");

        if (filteredAlbums.isEmpty()) {
            System.out.println("No albums found in this release year range.");
        } else {
            for (Album album : filteredAlbums) {
                System.out.println("  - " + album.getTitle() + " (" + album.getReleaseYear() + ")");
            }
        }
    }

    private void searchMultiCriteria() {
        System.out.println("\n--- Multi-Criteria Search ---");
        System.out.println("Enter search criteria (press Enter to skip any field):");

        String artistName = getOptionalStringInput("Artist name contains: ");
        String songTitle = getOptionalStringInput("Song title contains: ");
        String country = getOptionalStringInput("Artist country contains: ");

        System.out.println("\nMulti-criteria search results:");

        // Search artists
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();
        List<Artist> filteredArtists = new ArrayList<>();

        for (Artist artist : artists) {
            boolean matches = true;

            if (!artistName.isEmpty() &&
                (artist.getName() == null || !artist.getName().toLowerCase().contains(artistName.toLowerCase()))) {
                matches = false;
            }

            if (!country.isEmpty() &&
                (artist.getCountry() == null || !artist.getCountry().toLowerCase().contains(country.toLowerCase()))) {
                matches = false;
            }

            if (matches) {
                filteredArtists.add(artist);
            }
        }

        if (!filteredArtists.isEmpty()) {
            System.out.println("\nMatching Artists:");
            for (Artist artist : filteredArtists) {
                System.out.println("  - " + artist.getName() + " (" + artist.getCountry() + ")");
            }
        }

        // Search songs
        List<Song> songs = musicService.getSongDAO().getAllSongs();
        List<Song> filteredSongs = new ArrayList<>();

        for (Song song : songs) {
            boolean matches = true;

            if (!songTitle.isEmpty() &&
                (song.getTitle() == null || !song.getTitle().toLowerCase().contains(songTitle.toLowerCase()))) {
                matches = false;
            }

            if (matches) {
                filteredSongs.add(song);
            }
        }

        if (!filteredSongs.isEmpty()) {
            System.out.println("\nMatching Songs:");
            for (Song song : filteredSongs) {
                System.out.println("  - " + song.getTitle() + " [" + song.getFormattedDuration() + "]");
            }
        }

        if (filteredArtists.isEmpty() && filteredSongs.isEmpty()) {
            System.out.println("No results found matching the specified criteria.");
        }
    }

    private void showStatistics() {
        System.out.println("\n=== DATABASE STATISTICS ===");

        // Count entities
        List<Artist> artists = musicService.getArtistDAO().getAllArtists();
        List<Song> songs = musicService.getSongDAO().getAllSongs();
        List<Album> albums = musicService.getAlbumDAO().getAllAlbums();
        List<Genre> genres = musicService.getGenreDAO().getAllGenres();
        List<Award> awards = musicService.getAwardDAO().getAllAwards();

        System.out.println("Total Entities:");
        System.out.println("  - Artists: " + artists.size());
        System.out.println("  - Songs: " + songs.size());
        System.out.println("  - Albums: " + albums.size());
        System.out.println("  - Genres: " + genres.size());
        System.out.println("  - Awards: " + awards.size());

        // Calculate total duration
        int totalDuration = 0;
        int songsWithDuration = 0;
        for (Song song : songs) {
            if (song.getDuration() != null) {
                totalDuration += song.getDuration();
                songsWithDuration++;
            }
        }

        if (songsWithDuration > 0) {
            int hours = totalDuration / 3600;
            int minutes = (totalDuration % 3600) / 60;
            int seconds = totalDuration % 60;

            System.out.println("\nMusic Statistics:");
            System.out.println("  - Total music duration: " + hours + "h " + minutes + "m " + seconds + "s");
            System.out.println("  - Average song duration: " + (totalDuration / songsWithDuration) + " seconds");
        }

        // Year statistics
        if (!songs.isEmpty()) {
            Integer earliestYear = null;
            Integer latestYear = null;

            for (Song song : songs) {
                if (song.getReleaseYear() != null) {
                    if (earliestYear == null || song.getReleaseYear() < earliestYear) {
                        earliestYear = song.getReleaseYear();
                    }
                    if (latestYear == null || song.getReleaseYear() > latestYear) {
                        latestYear = song.getReleaseYear();
                    }
                }
            }

            if (earliestYear != null && latestYear != null) {
                System.out.println("\nYear Range:");
                System.out.println("  - Earliest song: " + earliestYear);
                System.out.println("  - Latest song: " + latestYear);
                System.out.println("  - Span: " + (latestYear - earliestYear) + " years");
            }
        }

        // Country statistics
        if (!artists.isEmpty()) {
            List<String> countries = new ArrayList<>();
            for (Artist artist : artists) {
                if (artist.getCountry() != null && !countries.contains(artist.getCountry())) {
                    countries.add(artist.getCountry());
                }
            }
            System.out.println("\nCountry Diversity:");
            System.out.println("  - Artists from " + countries.size() + " different countries");
        }

        // Relationship statistics
        int totalPerformances = 0;
        int totalArtistAwards = 0;
        int totalSongGenres = 0;

        for (Artist artist : artists) {
            totalPerformances += musicService.getSongsByArtist(artist.getArtistId()).size();
            totalArtistAwards += musicService.getAwardsByArtist(artist.getArtistId()).size();
        }

        for (Song song : songs) {
            totalSongGenres += musicService.getGenresBySong(song.getSongId()).size();
        }

        System.out.println("\nRelationship Statistics:");
        System.out.println("  - Total performances: " + totalPerformances);
        System.out.println("  - Total artist-award relationships: " + totalArtistAwards);
        System.out.println("  - Total song-genre relationships: " + totalSongGenres);
    }

    // Utility methods for input handling
    private String getStringInput(String prompt) {
        System.out.print(prompt);
        return scanner.nextLine().trim();
    }

    private String getOptionalStringInput(String prompt) {
        System.out.print(prompt);
        return scanner.nextLine().trim();
    }

    private int getIntInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt);
                return Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Please enter a valid number.");
            }
        }
    }

    private Integer getOptionalIntInput(String prompt) {
        System.out.print(prompt);
        String input = scanner.nextLine().trim();
        if (input.isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            System.out.println("Invalid number format. Skipping this field.");
            return null;
        }
    }
}
