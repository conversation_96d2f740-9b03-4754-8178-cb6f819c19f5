package controller;

import service.MusicService;
import model.Song;
import model.Album;
import util.InputHelper;
import java.util.List;

/**
 * Controller class for managing Song-related operations
 * Handles all song management functionality including CRUD operations
 */
public class SongController {
    
    private MusicService musicService;
    private InputHelper inputHelper;
    
    public SongController(MusicService musicService, InputHelper inputHelper) {
        this.musicService = musicService;
        this.inputHelper = inputHelper;
    }
    
    /**
     * Displays the song management menu and handles user choices
     */
    public void manageSongs() {
        System.out.println("\n=== SONG MANAGEMENT ===");
        System.out.println("1. Add Song");
        System.out.println("2. View All Songs");
        System.out.println("3. Search Songs");
        System.out.println("4. View Songs by Album");

        int choice = inputHelper.getIntInput("Enter your choice: ");

        switch (choice) {
            case 1:
                addSong();
                break;
            case 2:
                viewAllSongs();
                break;
            case 3:
                searchSongs();
                break;
            case 4:
                viewSongsByAlbum();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }
    
    /**
     * Adds a new song to the database
     */
    private void addSong() {
        System.out.println("\n--- Add New Song ---");
        String title = inputHelper.getStringInput("Enter song title: ");
        Integer duration = inputHelper.getOptionalIntInput("Enter duration in seconds (or press Enter to skip): ");
        Integer releaseYear = inputHelper.getOptionalIntInput("Enter release year (or press Enter to skip): ");

        Song song = new Song(title, duration, releaseYear);

        if (musicService.getSongDAO().createSong(song)) {
            System.out.println("Song added successfully! ID: " + song.getSongId());
            System.out.println("Note: To add this song to an album, use 'Manage Albums' -> 'Manage Album-Song Relationships'");
        } else {
            System.out.println("Failed to add song.");
        }
    }
    
    /**
     * Displays all songs in the database
     */
    private void viewAllSongs() {
        System.out.println("\n--- All Songs ---");
        List<Song> songs = musicService.getSongDAO().getAllSongs();

        if (songs.isEmpty()) {
            System.out.println("No songs found.");
        } else {
            for (Song song : songs) {
                System.out.println(song + " [Duration: " + song.getFormattedDuration() + "]");
            }
        }
    }
    
    /**
     * Searches for songs by title
     */
    private void searchSongs() {
        String searchTerm = inputHelper.getStringInput("Enter song title to search: ");
        List<Song> songs = musicService.getSongDAO().searchSongsByTitle(searchTerm);

        if (songs.isEmpty()) {
            System.out.println("No songs found matching: " + searchTerm);
        } else {
            System.out.println("Search results:");
            for (Song song : songs) {
                System.out.println(song + " [Duration: " + song.getFormattedDuration() + "]");
            }
        }
    }
    
    /**
     * Displays songs in a specific album
     */
    private void viewSongsByAlbum() {
        int albumId = inputHelper.getIntInput("Enter album ID: ");
        Album album = musicService.getAlbumDAO().getAlbumById(albumId);

        if (album == null) {
            System.out.println("Album not found.");
            return;
        }

        List<Song> songs = musicService.getSongsByAlbum(albumId);

        if (songs.isEmpty()) {
            System.out.println("No songs found for album: " + album.getTitle());
        } else {
            System.out.println("Songs in '" + album.getTitle() + "':");
            for (Song song : songs) {
                System.out.println(song + " [Duration: " + song.getFormattedDuration() + "]");
            }

            int totalSongs = musicService.getTotalSongsInAlbum(albumId);
            System.out.println("\nTotal songs in album: " + totalSongs);
        }
    }
}
